<script lang="ts">
	import { But<PERSON> } from "../ui/button";
	import { NavChevronDownIcon } from "../icons/ui";
	import { hasChildren } from "$lib/utils/index";
	import { page } from "$app/stores";
	import { isLinkActive } from "$lib/utils/index";
	import type { NavLink } from "$types";

	/**
	 * Props interface for the navigation dropdown button component
	 */
	interface Props {
		link: NavLink;
		variant?: "default" | "ghost" | "outline";
		class?: string;
		activeDropdownId: string | null;
		onTriggerEnter: (linkId: string) => void;
		onTriggerLeave: () => void;
	}

	let {
		link,
		variant = "ghost",
		class: className = "",
		activeDropdownId,
		onTriggerEnter,
		onTriggerLeave
	}: Props = $props();

	/**
	 * Determines if this dropdown is currently active/open
	 */
	const isDropdownActive = $derived(activeDropdownId === link.label);

	/**
	 * <PERSON><PERSON> mouse enter event for dropdown trigger
	 */
	function handleMouseEnter() {
		if (hasChildren(link)) {
			onTriggerEnter(link.label);
		}
	}

	/**
	 * Handles mouse leave event for dropdown trigger
	 */
	function handleMouseLeave() {
		if (hasChildren(link)) {
			onTriggerLeave();
		}
	}

	/**
	 * Determines if this link is currently active
	 */
	const isActive = $derived(isLinkActive($page.url.pathname, link.href));
</script>

{#if !hasChildren(link)}
	<!-- Simple navigation link without dropdown -->
	<Button
		{variant}
		class={`cursor-pointer ${className} ${isActive ? "text-blackwhite" : ""}`}
		href={link.href}
	>
		{link.label}
	</Button>
{:else}
	<!-- Navigation link with dropdown functionality -->
	<Button
		{variant}
		class={`relative ${className}`}
		href={link.href}
		onmouseenter={handleMouseEnter}
		onmouseleave={handleMouseLeave}
	>
		<!-- Button content with chevron indicator -->
		<div class="flex flex-row items-center justify-center gap-1">
			{link.label}
			<NavChevronDownIcon />
		</div>

		<!-- Dropdown menu - only rendered when active -->
		{#if isDropdownActive}
			<div
				class="bg-background text-muted-foreground absolute left-1/2 top-full z-[100] flex -translate-x-1/2 flex-col gap-2 rounded-md border p-2 shadow-md"
			>
				{#each link.children as childLink (childLink.label)}
					<a
						href={childLink.href}
						class="hover:bg-muted rounded px-2 py-1 text-sm transition-colors"
					>
						{childLink.label}
					</a>
				{/each}
			</div>
		{/if}
	</Button>
{/if}
