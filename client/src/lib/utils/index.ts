import { cn, type WithoutChild, type WithoutChildren, type WithoutChildrenOrChild } from "../utils";
import { isTailwindColorClass, processColor, combineClasses, handleIconColor } from "./colorUtils";
import {
	getDefaultNavLinks,
	getAllLeafLinks,
	getLoginLink,
	getScheduleDemoLink,
	getTopLevelLinks,
	hasChildren,
	getChildren,
	findLinkByLabel,
	getNavigationDepth,
	getLinksAtLevel,
	getBreadcrumbPath,
	getNavigationStats,
	isLinkActive
} from "./navLinksUtils";

export {
	cn,
	type WithoutChild,
	type WithoutChildren,
	type WithoutChildrenOrChild,
	// Color utilities
	isTailwindColorClass,
	processColor,
	combineClasses,
	handleIconColor,
	// Navigation link utilities
	getDefaultNavLinks,
	getAllLeafLinks,
	getLoginLink,
	getScheduleDemoLink,
	getTopLevelLinks,
	hasChildren,
	getChildren,
	findLinkByLabel,
	getNavigationDepth,
	getLinksAtLevel,
	getBreadcrumbPath,
	getNavigationStats,
	isLinkActive
};
