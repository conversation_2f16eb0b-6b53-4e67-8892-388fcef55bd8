/**
 * Utility functions for handling Tailwind CSS color classes and color props.
 *
 * Detects Tailwind text/fill color classes, processes colors for SVG icons,
 * and combines class strings ensuring color precedence.
 */

/**
 * Checks if a string is a Tailwind color class (text-* or fill-*)
 * @param value - The value to check
 * @returns True if the value is a Tailwind color class
 * @example
 * ```ts
 * isTailwindColorClass('text-red-500') // true
 * isTailwindColorClass('fill-blue-600') // true
 * isTailwindColorClass('#ff0000') // false
 * ```
 */
export function isTailwindColorClass(value: string): boolean {
	return typeof value === "string" && /^(text-|fill-)/.test(value);
}

/**
 * Processes color prop - if it's a Tailwind class, returns 'currentColor'
 * so the class can handle the styling, otherwise returns the color value
 * @param color - Color value or Tailwind class
 * @returns Processed color value
 * @example
 * ```ts
 * processColor('text-red-500') // 'currentColor'
 * processColor('#ff0000') // '#ff0000'
 * processColor('red') // 'red'
 * ```
 */
export function processColor(color: string): string {
	if (isTailwindColorClass(color)) {
		return "currentColor";
	}
	return color;
}

/**
 * Combines className with color class if color is a Tailwind class.
 * Removes any existing Tailwind color classes from className to ensure
 * the passed color always takes precedence.
 * @param className - Existing class string
 * @param color - Color value or Tailwind class
 * @returns Combined class string
 * @example
 * ```ts
 * combineClasses('icon', 'text-red-500') // 'icon text-red-500'
 * combineClasses('icon text-blue-400', 'text-red-500') // 'icon text-red-500'
 * combineClasses('icon fill-green-300', 'text-red-500') // 'icon text-red-500'
 * combineClasses('icon', '#ff0000') // 'icon'
 * combineClasses('', 'fill-blue-600') // 'fill-blue-600'
 * ```
 */
export function combineClasses(className: string, color: string): string {
	if (isTailwindColorClass(color)) {
		// Remove existing Tailwind color classes from className
		const filteredClasses = className
			.split(" ")
			.filter((cls) => cls && !isTailwindColorClass(cls))
			.join(" ");

		return `${filteredClasses} ${color}`.trim();
	}
	return className;
}

/**
 * Handles icon color and class combining for SVG icons.
 * If color is a Tailwind class, adds it to className and returns 'currentColor' for the color.
 * Removes any existing Tailwind color classes from className to ensure the passed color takes precedence.
 * Otherwise returns the original color and className unchanged.
 * @param className - Existing class string
 * @param color - Color value or Tailwind class
 * @returns Object with processed color and combined className
 * @example
 * ```ts
 * handleIconColor('icon', 'text-red-500')
 * // { color: 'currentColor', className: 'icon text-red-500' }
 *
 * handleIconColor('icon text-blue-400', 'text-red-500')
 * // { color: 'currentColor', className: 'icon text-red-500' }
 *
 * handleIconColor('icon', '#ff0000')
 * // { color: '#ff0000', className: 'icon' }
 *
 * handleIconColor('', 'fill-blue-600')
 * // { color: 'currentColor', className: 'fill-blue-600' }
 * ```
 */
export function handleIconColor(
	className: string,
	color: string
): { color: string; className: string } {
	if (isTailwindColorClass(color)) {
		return {
			color: "currentColor",
			className: combineClasses(className, color)
		};
	}
	return {
		color,
		className
	};
}
