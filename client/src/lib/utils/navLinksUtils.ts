//utils/navLinks.ts

import { type NavLink, type Leaf<PERSON><PERSON><PERSON>ink, type NavLinkKind } from "$types";

/**
 * Recursively finds all leaf navigation links of a specific kind.
 * @param links - Array of navigation links to search through
 * @param kind - The kind of navigation link to find
 * @returns Array of leaf navigation links matching the specified kind
 */
function findLeafLinksByKind(links: NavLink[], kind: NavLinkKind): LeafNavLink[] {
	const result: LeafNavLink[] = [];

	for (const link of links) {
		if (link.children) {
			// Recursively search through children
			result.push(...findLeafLinksByKind(link.children, kind));
		} else if (link.kind === kind) {
			// It's a leaf link with the matching kind
			result.push(link);
		}
	}

	return result;
}

/**
 * Gets all default navigation links (recursively searches through nested structures).
 * @param links - Array of navigation links to search through
 * @returns Array of default leaf navigation links
 */
export function getDefaultNavLinks(links: NavLink[]): LeafNavLink[] {
	return findLeafLinksByKind(links, "default");
}

/**
 * Gets the login navigation link.
 * @param links - Array of navigation links to search through
 * @returns The login link if found, undefined otherwise
 */
export function getLoginLink(links: NavLink[]): LeafNavLink {
	const loginLinks = findLeafLinksByKind(links, "login");
	return loginLinks[0]; // Return first login link found
}

/**
 * Gets the schedule demo navigation link.
 * @param links - Array of navigation links to search through
 * @returns The schedule demo link if found, undefined otherwise
 */
export function getScheduleDemoLink(links: NavLink[]): LeafNavLink {
	const scheduleDemoLinks = findLeafLinksByKind(links, "schedule-demo");
	return scheduleDemoLinks[0]; // Return first schedule demo link found
}

/**
 * Gets all leaf navigation links of any kind (flattens the entire navigation structure).
 * @param links - Array of navigation links to search through
 * @returns Array of all leaf navigation links
 */
export function getAllLeafLinks(links: NavLink[]): LeafNavLink[] {
	const result: LeafNavLink[] = [];

	for (const link of links) {
		if (link.children) {
			result.push(...getAllLeafLinks(link.children));
		} else {
			result.push(link);
		}
	}

	return result;
}

// NEW FUNCTIONS FOR DROPDOWN NAVIGATION

/**
 * Gets all top-level navigation links (level 1).
 * @param links - Array of navigation links
 * @returns Array of top-level navigation links
 */
export function getTopLevelLinks(links: NavLink[]): NavLink[] {
	return [...links]; // Return a copy of the top-level array
}

/**
 * Checks if a navigation link has children (is a dropdown).
 * @param link - Navigation link to check
 * @returns True if the link has children, false otherwise
 */
export function hasChildren(link: NavLink): link is NavLink & { children: NavLink[] } {
	return "children" in link && Array.isArray(link.children);
}

/**
 * Gets the children of a navigation link if it has any.
 * @param link - Navigation link to get children from
 * @returns Array of child links, or empty array if no children
 */
export function getChildren(link: NavLink): NavLink[] {
	return hasChildren(link) ? link.children : [];
}

/**
 * Finds a navigation link by its label (searches recursively).
 * @param links - Array of navigation links to search through
 * @param label - Label of the link to find
 * @returns The found link or undefined
 */
export function findLinkByLabel(links: NavLink[], label: string): NavLink | undefined {
	for (const link of links) {
		if (link.label === label) {
			return link;
		}
		if (hasChildren(link)) {
			const found = findLinkByLabel(link.children, label);
			if (found) return found;
		}
	}
	return undefined;
}

/**
 * Gets the depth/level of nesting for navigation links.
 * @param links - Array of navigation links
 * @returns Maximum depth of the navigation structure
 */
export function getNavigationDepth(links: NavLink[]): number {
	let maxDepth = 1;

	for (const link of links) {
		if (hasChildren(link)) {
			const childDepth = 1 + getNavigationDepth(link.children);
			maxDepth = Math.max(maxDepth, childDepth);
		}
	}

	return maxDepth;
}

/**
 * Gets all links at a specific level/depth.
 * @param links - Array of navigation links
 * @param targetLevel - The level to get links from (1-based)
 * @param currentLevel - Current level being processed (internal use)
 * @returns Array of links at the specified level
 */
export function getLinksAtLevel(
	links: NavLink[],
	targetLevel: number,
	currentLevel: number = 1
): NavLink[] {
	if (targetLevel === currentLevel) {
		return [...links];
	}

	const result: NavLink[] = [];
	for (const link of links) {
		if (hasChildren(link)) {
			result.push(...getLinksAtLevel(link.children, targetLevel, currentLevel + 1));
		}
	}

	return result;
}

/**
 * Builds a breadcrumb path to a specific link.
 * @param links - Array of navigation links to search through
 * @param targetLabel - Label of the target link
 * @param currentPath - Current path being built (internal use)
 * @returns Array of labels forming the breadcrumb path, or empty array if not found
 */
export function getBreadcrumbPath(
	links: NavLink[],
	targetLabel: string,
	currentPath: string[] = []
): string[] {
	for (const link of links) {
		const newPath = [...currentPath, link.label];

		if (link.label === targetLabel) {
			return newPath;
		}

		if (hasChildren(link)) {
			const found = getBreadcrumbPath(link.children, targetLabel, newPath);
			if (found.length > 0) return found;
		}
	}

	return [];
}

/**
 * Gets navigation statistics for debugging/info purposes.
 * @param links - Array of navigation links
 * @returns Object with navigation statistics
 */
export function getNavigationStats(links: NavLink[]) {
	const stats = {
		totalLinks: 0,
		leafLinks: 0,
		parentLinks: 0,
		maxDepth: 0,
		linksByKind: {} as Record<NavLinkKind, number>
	};

	function countLinks(navLinks: NavLink[], depth: number = 1) {
		stats.maxDepth = Math.max(stats.maxDepth, depth);

		for (const link of navLinks) {
			stats.totalLinks++;

			if (hasChildren(link)) {
				stats.parentLinks++;
				countLinks(link.children, depth + 1);
			} else {
				stats.leafLinks++;
				stats.linksByKind[link.kind] = (stats.linksByKind[link.kind] || 0) + 1;
			}
		}
	}

	countLinks(links);
	return stats;
}

/**
 * Checks if a link is currently active based on the current page URL.
 * @param currentPath - Current page path (from $page.url.pathname)
 * @param linkHref - Href of the link to check
 * @returns True if the link is active, false otherwise
 */
export function isLinkActive(currentPath: string, linkHref?: string): boolean {
	if (!linkHref) return false;
	if (linkHref === "/") {
		return currentPath === "/";
	}
	return currentPath.startsWith(linkHref);
}
