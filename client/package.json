{"name": "client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "dependencies": {"@iconify/svelte": "^5.0.0", "@lucide/svelte": "^0.482.0", "@tanstack/table-core": "^8.21.3", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.6.0", "formsnap": "^2.0.1", "gsap": "^3.13.0", "layerchart": "2.0.0-next.6", "mode-watcher": "^1.0.7", "paneforge": "1.0.0-next.5", "svelte-sonner": "^1.0.2", "sveltekit-superforms": "^2.25.0", "tailwind-merge": "^3.3.0", "tailwind-variants": "^0.2.1", "tw-animate-css": "^1.3.0", "vaul-svelte": "1.0.0-next.7", "bits-ui": "^2.4.1"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.1", "@sveltejs/adapter-vercel": "^5.6.3", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}